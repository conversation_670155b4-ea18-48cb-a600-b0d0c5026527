<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - نمو EMS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }

        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #32C3E5;
        }

        h2 {
            font-size: 1.2rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        p {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .retry-button {
            background: linear-gradient(135deg, #32C3E5 0%, #2AC4DD 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(50, 195, 229, 0.4);
        }

        .features-list {
            text-align: right;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .features-list h3 {
            margin-bottom: 15px;
            color: #34D09F;
        }

        .features-list ul {
            list-style: none;
        }

        .features-list li {
            margin: 8px 0;
            padding-right: 20px;
            position: relative;
        }

        .features-list li::before {
            content: "✓";
            position: absolute;
            right: 0;
            color: #34D09F;
            font-weight: bold;
        }

        .network-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .network-status.online {
            background: rgba(52, 208, 159, 0.2);
            border: 1px solid rgba(52, 208, 159, 0.3);
        }

        .network-status.offline {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .offline-container {
                padding: 30px 20px;
                margin: 10px;
            }

            h1 {
                font-size: 1.5rem;
            }

            h2 {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1>غير متصل بالإنترنت</h1>
        <h2>Offline Mode</h2>
        
        <p>
            يبدو أنك غير متصل بالإنترنت حالياً. لا تقلق، يمكنك الاستمرار في استخدام بعض ميزات نظام نمو.
        </p>

        <div class="features-list">
            <h3>الميزات المتاحة في وضع عدم الاتصال:</h3>
            <ul>
                <li>عرض البيانات المحفوظة مسبقاً</li>
                <li>تصفح الموظفين والأقسام</li>
                <li>عرض التقارير المحفوظة</li>
                <li>استخدام الآلة الحاسبة والأدوات</li>
                <li>حفظ التغييرات للمزامنة لاحقاً</li>
            </ul>
        </div>

        <button class="retry-button" onclick="checkConnection()">
            إعادة المحاولة
        </button>
        
        <button class="retry-button" onclick="goHome()" style="background: rgba(255, 255, 255, 0.1);">
            العودة للرئيسية
        </button>

        <div class="network-status" id="networkStatus">
            <span id="statusText">جاري فحص الاتصال...</span>
        </div>
    </div>

    <script>
        // Check network status
        function updateNetworkStatus() {
            const statusElement = document.getElementById('networkStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'network-status online';
                statusText.textContent = 'متصل بالإنترنت - يمكنك إعادة تحميل الصفحة';
            } else {
                statusElement.className = 'network-status offline';
                statusText.textContent = 'غير متصل بالإنترنت';
            }
        }

        // Check connection and refresh if online
        function checkConnection() {
            if (navigator.onLine) {
                // FIXED: Use postMessage to communicate with main app
                if (window.parent !== window) {
                    window.parent.postMessage({ type: 'REFRESH_APP' }, '*');
                } else {
                    // Fallback for standalone page
                    window.location.reload();
                }
            } else {
                alert('لا يزال الاتصال بالإنترنت غير متاح. يرجى التحقق من اتصالك والمحاولة مرة أخرى.');
            }
        }

        // Go to home page
        function goHome() {
            // FIXED: Use postMessage to communicate with main app
            if (window.parent !== window) {
                window.parent.postMessage({ type: 'NAVIGATE', path: '/' }, '*');
            } else {
                // Fallback for standalone page
                window.location.href = '/';
            }
        }

        // Listen for network status changes
        window.addEventListener('online', () => {
            updateNetworkStatus();
            setTimeout(() => {
                if (confirm('تم استعادة الاتصال بالإنترنت. هل تريد تحديث التطبيق؟')) {
                    // FIXED: Use postMessage to communicate with main app
                    if (window.parent !== window) {
                        window.parent.postMessage({ type: 'REFRESH_APP' }, '*');
                    } else {
                        window.location.reload();
                    }
                }
            }, 1000);
        });

        window.addEventListener('offline', updateNetworkStatus);

        // Initial status check
        updateNetworkStatus();

        // Periodic connection check
        setInterval(() => {
            updateNetworkStatus();
        }, 5000);

        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
            });
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', () => {
            // Add click effect to buttons
            const buttons = document.querySelectorAll('.retry-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // Add keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    checkConnection();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            });
        });
    </script>
</body>
</html>
