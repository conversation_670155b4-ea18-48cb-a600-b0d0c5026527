<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين التطبيق - Reset App</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', system-ui, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .reset-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            max-width: 500px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            margin-bottom: 20px;
            font-size: 2rem;
        }
        
        p {
            margin-bottom: 30px;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .danger {
            background: rgba(239, 68, 68, 0.3);
            border-color: rgba(239, 68, 68, 0.5);
        }
        
        .danger:hover {
            background: rgba(239, 68, 68, 0.5);
        }
        
        .success {
            background: rgba(34, 197, 94, 0.3);
            border-color: rgba(34, 197, 94, 0.5);
        }
        
        .success:hover {
            background: rgba(34, 197, 94, 0.5);
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.2);
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <h1>🔧 إعادة تعيين التطبيق</h1>
        <h2>App Reset Tool</h2>
        
        <p>
            إذا كان التطبيق عالق في شاشة التحميل، استخدم هذه الأدوات لإعادة تعيين الحالة.
            <br><br>
            If the app is stuck on loading screen, use these tools to reset the state.
        </p>
        
        <button onclick="clearStorage()" class="danger">
            🗑️ مسح جميع البيانات المحلية<br>
            Clear All Local Data
        </button>
        
        <button onclick="clearAuthOnly()">
            🔑 مسح بيانات المصادقة فقط<br>
            Clear Auth Data Only
        </button>
        
        <button onclick="goToLogin()" class="success">
            🏠 الذهاب لصفحة تسجيل الدخول<br>
            Go to Login Page
        </button>
        
        <button onclick="goToHome()">
            🏡 الذهاب للصفحة الرئيسية<br>
            Go to Home Page
        </button>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        function showStatus(message) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';
        }
        
        function clearStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                showStatus('✅ All local data cleared successfully!\nجميع البيانات المحلية تم مسحها بنجاح!');
                
                setTimeout(() => {
                    // FIXED: Use postMessage to communicate with main app
                    if (window.parent !== window) {
                        window.parent.postMessage({ type: 'NAVIGATE', path: '/' }, '*');
                    } else {
                        window.location.href = '/';
                    }
                }, 2000);
            } catch (error) {
                showStatus('❌ Error clearing data: ' + error.message);
            }
        }
        
        function clearAuthOnly() {
            try {
                localStorage.removeItem('token');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('user');
                
                showStatus('✅ Auth data cleared successfully!\nبيانات المصادقة تم مسحها بنجاح!');
                
                setTimeout(() => {
                    // FIXED: Use postMessage to communicate with main app
                    if (window.parent !== window) {
                        window.parent.postMessage({ type: 'NAVIGATE', path: '/login' }, '*');
                    } else {
                        window.location.href = '/login';
                    }
                }, 1500);
            } catch (error) {
                showStatus('❌ Error clearing auth data: ' + error.message);
            }
        }
        
        function goToLogin() {
            // FIXED: Use postMessage to communicate with main app
            if (window.parent !== window) {
                window.parent.postMessage({ type: 'NAVIGATE', path: '/login' }, '*');
            } else {
                window.location.href = '/login';
            }
        }

        function goToHome() {
            // FIXED: Use postMessage to communicate with main app
            if (window.parent !== window) {
                window.parent.postMessage({ type: 'NAVIGATE', path: '/' }, '*');
            } else {
                window.location.href = '/';
            }
        }
        
        // Auto-check current storage
        window.onload = function() {
            const hasToken = localStorage.getItem('token') || localStorage.getItem('access_token');
            const hasUser = localStorage.getItem('user');
            
            if (hasToken || hasUser) {
                showStatus('⚠️ Found existing auth data in storage\nتم العثور على بيانات مصادقة في التخزين المحلي');
            } else {
                showStatus('ℹ️ No auth data found in storage\nلا توجد بيانات مصادقة في التخزين المحلي');
            }
        };
    </script>
</body>
</html>
